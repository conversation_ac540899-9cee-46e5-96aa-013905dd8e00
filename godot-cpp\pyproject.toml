[tool.mypy]
disallow_any_generics = true
explicit_package_bases = true
ignore_missing_imports = true
namespace_packages = true
no_implicit_optional = true
pretty = true
scripts_are_modules = true
show_column_numbers = true
warn_redundant_casts = true
warn_return_any = true
warn_unreachable = true

[tool.ruff]
extend-include = ["SConstruct"]
line-length = 120
target-version = "py37"

[tool.ruff.lint]
extend-select = [
	"I", # isort
]

[tool.ruff.lint.per-file-ignores]
"SConstruct" = [
	"F821", # Undefined name
]

[tool.codespell]
enable-colors = ""
write-changes = ""
check-hidden = ""
quiet-level = 3
builtin = "clear,rare,en-GB_to_en-US"
ignore-words-list = """\
	breaked,
	cancelled,
	checkin,
	curvelinear,
	doubleclick,
	expct,
	findn,
	gird,
	hel,
	inout,
	labelin,
	lod,
	mis,
	nd,
	numer,
	ot,
	outin,
	requestor,
	te,
	textin,
	thirdparty,
	vai
"""
