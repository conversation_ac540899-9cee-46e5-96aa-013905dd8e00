package godot

import __bindgen_gde "godot:gdext"

{% for class in this.classes %}
{{ class.name }} :: {{ class.derives }}
{% end %}

{% for enum_ in this.enums %}
{% embed "bindgen_view_enum.temple.twig" with enum_ %}
{% end %}

{% for bit_field_ in this.bit_fields %}
{% embed "bindgen_view_bit_field.temple.twig" with bit_field_ %}
{% end %}

init :: proc "contextless" () {
    __name: String_Name
{% for function in this.functions %}
    __name = new_string_name_cstring("{{ function.godot_name }}", true)
    __{{ function.name }}_ptr = __bindgen_gde.variant_get_ptr_utility_function(&__name, {{ i64(function.hash) }})
{% end %}
{% for singleton in this.singletons %}
    __{{ singleton.name }}_name = new_string_name_cstring("{{ singleton.name }}", true)
{% end %}
{% for snake_name in this.inits %}
    {{ snake_name }}_init()
{% end %}
}

{% for function in this.functions %}
{% if return_type, has_return_type := function.return_type.(string); has_return_type %}
gd_{{ function.name }} :: proc "contextless" (
{% for arg in function.args %}
    {{ arg.name }}_: {{ arg.type }},
{% end %}
) -> (ret: {{ return_type }}) {
{% for arg in function.args %}
    {{ arg.name }}_ := {{ arg.name }}_
{% end %}
    args := []__bindgen_gde.TypePtr {
{% for arg in function.args %}
        &{{ arg.name }}_,
{% end %}
    }
    __{{ function.name }}_ptr(&ret, raw_data(args), len(args))
    return
}

{% else %}
gd_{{ function.name }} :: proc "contextless" (
{% for arg in function.args %}
    {{ arg.name }}_: {{ arg.type }},
{% end %}
) {
{% for arg in function.args %}
    {{ arg.name }}_ := {{ arg.name }}_
{% end %}
    args := []__bindgen_gde.TypePtr {
{% for arg in function.args %}
        &{{ arg.name }}_,
{% end %}
    }
    __{{ function.name }}_ptr(nil, raw_data(args), len(args))
}

{% end %}
{% end %}

{% for singleton in this.singletons %}
singleton_{{ singleton.snake_name }} :: proc "contextless" () -> {{ singleton.type }} {
    @(static) __ptr: __bindgen_gde.ObjectPtr
    if __ptr == nil {
        __ptr = __bindgen_gde.global_get_singleton(&__{{ singleton.name }}_name)
    }
    
    return __ptr
}
{% end %}

@(require_results)
cast_class :: proc "contextless" (object: $T, $C: typeid) -> (result: C, ok: bool) {
    class_tag := __bindgen_gde.classdb_get_class_tag(class_name_ref(C))
    result_ptr := __bindgen_gde.object_cast_to(object, class_tag)
    if result_ptr != nil {
        return (cast(C)result_ptr), true
    }
    return C{}, false
}

class_name_ref :: proc "contextless" ($C: typeid) -> ^String_Name {
    switch typeid_of(C) {
{% for class in this.classes %}
    case typeid_of({{ class.name }}): return {{ class.snake_name }}_name_ref()
{% end %}
    }
    return nil
}

{% for function in this.functions %}
@(private="file")
__{{ function.name }}_ptr: __bindgen_gde.PtrUtilityFunction
{% end %}

{% for singleton in this.singletons %}
@(private="file")
__{{ singleton.name }}_name: String_Name
{% end %}