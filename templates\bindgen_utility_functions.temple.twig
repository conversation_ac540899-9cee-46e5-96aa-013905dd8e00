/* This file is generated by godin bindgen - DO NOT EDIT! */
package core

import __bindgen_gde "../gdextension"
import __bindgen_variant  "../variant"

{% for function in this.utility_functions %}
{{ function.odin_name }} :: proc "contextless" (
{% for argument, i in function.arguments %}
{% if i > 0 %}, {% end %}{{argument.name}}: {% if _, is_class := argument.type.derived.(StateClass); is_class %}__bindgen_variant.{% end %}{{ argument.type.odin_type }}
{% end %}) {% if return_type, has_return_type := function.return_type.(^StateType); has_return_type %} -> {% if _, is_class := return_type.derived.(StateClass); is_class %}__bindgen_variant.{% end %}{{ return_type.odin_type }}{% end %} {
{% for argument in function.arguments %}
    {{ argument.name }} := {{ argument.name }}
{% end %}
{% if return_type, has_return_type := function.return_type.(^StateType); has_return_type %}
    return __bindgen_gde.call_utility_function_ptr_ret(__{{ function.godot_name }}__Ptr, {% if _, is_class := return_type.derived.(StateClass); is_class %}__bindgen_variant.{% end %}{{ return_type.odin_type }}
{% else %}
    __bindgen_gde.call_utility_function_ptr_no_ret(__{{function.godot_name}}__Ptr
{% end %}
{% for argument in function.arguments %}, cast(__bindgen_gde.TypePtr)&{{ argument.name }}{% end %})
}

{% end %}

@(private)
__init_util_functions :: proc "contextless" () {
    _gde_name: __bindgen_variant.StringName
{% for function in this.utility_functions %}
    __bindgen_gde.string_name_new_with_latin1_chars(cast(__bindgen_gde.StringNamePtr)&_gde_name, "{{ function.godot_name }}", true)
    __{{ function.godot_name }}__Ptr = __bindgen_gde.variant_get_ptr_utility_function(cast(__bindgen_gde.StringNamePtr)&_gde_name, {{ i64(function.hash) }})
{% end %}
}

{% for function in this.utility_functions %}
@(private="file")
__{{ function.godot_name }}__Ptr: __bindgen_gde.PtrUtilityFunction
{% end %}
