name: 📊 Static Checks
on:
  workflow_call:

concurrency:
  group: ci-${{ github.actor }}-${{ github.head_ref || github.run_number }}-${{ github.ref }}-static
  cancel-in-progress: true

jobs:
  static-checks:
    name: Format (clang-format, ruff format, file format)
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Get changed files
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            files=$(git diff-tree --no-commit-id --name-only -r HEAD^1..HEAD 2> /dev/null || true)
          elif [ "${{ github.event_name }}" == "push" -a "${{ github.event.forced }}" == "false" -a "${{ github.event.created }}" == "false" ]; then
            files=$(git diff-tree --no-commit-id --name-only -r ${{ github.event.before }}..${{ github.event.after }} 2> /dev/null || true)
          fi
          files=$(echo "$files" | grep -v 'thirdparty' | xargs -I {} sh -c 'echo "\"./{}\""' | tr '\n' ' ')
          echo "CHANGED_FILES=$files" >> $GITHUB_ENV

      - name: Style checks via pre-commit
        uses: pre-commit/action@v3.0.1
        with:
          extra_args: --verbose --hook-stage manual --files ${{ env.CHANGED_FILES }}

      - name: Check generated files consistency
        run:
          python misc/scripts/check_get_file_list.py
