[gd_scene load_steps=3 format=3 uid="uid://c186aybk0cgr4"]

[ext_resource type="Script" uid="uid://bdgtgv5plcpgc" path="res://player/camera.gd" id="1_1ew6o"]

[sub_resource type="CylinderShape3D" id="CylinderShape3D_42akx"]
margin = 0.005
height = 1.75

[node name="Player" type="Player"]

[node name="PlayerShape" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.875, 0)
shape = SubResource("CylinderShape3D_42akx")

[node name="CameraYaw" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.65, 0)

[node name="Camera" type="Camera3D" parent="<PERSON>Yaw"]
script = ExtResource("1_1ew6o")

[node name="MouseMove" type="Node3D" parent="CameraYaw"]
