/* This file is generated by godin bindgen - DO NOT EDIT! */
package {{ this.derived.(StateClass).api_type }}

import __bindgen_gde "../gdextension"
import __bindgen_var "../variant"
{% if this.depends_on_core_math %}
import __bindgen_math "core:math"
{% end %}

{{ this.odin_type }} :: distinct __bindgen_gde.ObjectPtr

// enums
{% for state_enum in this.derived.(StateClass).enums %}

{% if !state_enum.odin_skip %}
{% embed "bindgen_enum.temple.twig" with state_enum.derived.(StateEnum) %}
{% end %}

{% end %}

// constants
{% for constant in this.derived.(StateClass).constants %}
{% if !constant.odin_skip %}
{% if constant.type != nil %}
{{ constant.name }}: {{ constant.type.odin_type }} = {% if int_value, is_int := constant.value.(int); is_int %}{{ int(int_value) }}{% else %}{{ constant.value.(string) }}{% end %}
{% else %}
{{ constant.name }} :: {% if int_value, is_int := constant.value.(int); is_int %}{{ int(int_value) }}{% else %}{{ constant.value.(string) }}{% end %}
{% end %}
{% end %}
{% end %}

// method frontends
{% for method in this.derived.(StateClass).methods %}
{% if !method.odin_skip %}
{{ method.odin_name }} :: proc "contextless" (self: {{ this.odin_type }}
{% for argument in method.arguments %}, {{ argument.name }}_: {{ bindgen_class_reference_type(argument.type) }}
{% end %})
{% if return_type, has_return_type := method.return_type.(^StateType); has_return_type %} -> {{ bindgen_class_reference_type(return_type) }}{% end %} {
    self := self
{% for argument in method.arguments %}
    {{ argument.name }}_ := {{ argument.name }}_
{% end %}
{% if return_type, has_return_type := method.return_type.(^StateType); has_return_type %}
    return __bindgen_gde.call_method_ptr_ret(
        __{{ method.odin_name }}__backing_ptr,
        {{ bindgen_class_reference_type(return_type) }},
        self,
{% else %}
    __bindgen_gde.call_method_ptr_no_ret(
        __{{ method.odin_name }}__backing_ptr,
        self,
{% end %}
{% for argument in method.arguments %}
        &{{ argument.name }}_,
{% end %}
    )
}

{% end %}
{% end %}

@(private)
__{{ this.odin_type }}_init :: proc "contextless" () {
    __bindgen_gde.string_name_new_with_latin1_chars(&__{{ this.odin_type }}__class_name, "{{ this.godot_type }}", true)
    _gde_name := __bindgen_var.StringName{}

{% for method in this.derived.(StateClass).methods %}
{% if !method.odin_skip %}
    __bindgen_gde.string_name_new_with_latin1_chars(&_gde_name, "{{ method.godot_name }}", true)
    __{{ method.odin_name }}__backing_ptr = __bindgen_gde.classdb_get_method_bind(&__{{ this.odin_type }}__class_name, &_gde_name, {{ i64(method.hash) }})
    _gde_name = __bindgen_var.StringName{}
{% end %}
{% end %}
}

new_{{ this.snake_type }} :: proc "contextless" () -> {{ this.odin_type }} {
    return cast({{ this.odin_type }})__bindgen_gde.classdb_construct_object(&__{{ this.odin_type }}__class_name)
}

@(private)
__{{ this.odin_type }}__class_name: __bindgen_var.StringName

// method backing ptrs
{% for method in this.derived.(StateClass).methods %}
{% if !method.odin_skip %}
@(private="file")
__{{ method.odin_name }}__backing_ptr: __bindgen_gde.MethodBindPtr
{% end %}
{% end %}
