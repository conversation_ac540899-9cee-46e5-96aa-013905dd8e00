godin is a tool to aid Godot dev with Odin 
Usage: 
    godin <command|topic> [args...] 
    
Commands: 
    build - process Odin files with Godin syntax 
    
Topics: 
    syntax - special syntax for describing classdb objects 
    
For more information about a command or a topic, invoke help command: 
    e.g. `godin help build` or `godin help syntax` 
    
You may also just invoke the topic on its own to see its help: 
    e.g. `godin syntax`
