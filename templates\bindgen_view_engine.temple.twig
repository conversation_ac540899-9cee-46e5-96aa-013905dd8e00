package godot

{% for name, import_ in this.imports %}
import {{ name }} "{{ import_.path }}"
{% end %}

{{ this.name }}_Constants :: enum {
{% for constant in this.file_constants %}
    {{ constant.name }} = {{ constant.value }},
{% end %}
}

{% for enum_ in this.enums %}
{% embed "bindgen_view_enum.temple.twig" with enum_ %}
{% end %}

{% for bit_field_ in this.bit_fields %}
{% embed "bindgen_view_bit_field.temple.twig" with bit_field_ %}
{% end %}

{{ this.snake_name }}_name_ref :: proc "contextless" () -> ^String_Name {
    return &__class_name
}

{{ this.snake_name }}_name :: proc "contextless" () -> String_Name {
    return __class_name
}

new_{{ this.snake_name }} :: proc "contextless" () -> {{ this.self }} {
{% if this.cast_on_new %}
    return cast({{ this.self }})__bindgen_gde.classdb_construct_object({{ this.snake_name }}_name_ref())
{% else %}
    return __bindgen_gde.classdb_construct_object({{ this.snake_name }}_name_ref())
{% end %}
}

// methods
{% for method in this.static_methods %}
{% if return_type, has_return_type := method.return_type.(string); has_return_type %}
{{ this.snake_name }}_{{ method.name }} :: proc "contextless" (
{% for arg in method.args %}
    {{ arg.name }}_: {{ arg.type }},
{% end %}
) -> (ret: {{ return_type }}) {
{% for arg in method.args %}
    {{ arg.name }}_ := {{ arg.name }}_
{% end %}
    args := []__bindgen_gde.TypePtr {
{% for arg in method.args %}
        &{{ arg.name }}_,
{% end %}
    }
    __bindgen_gde.object_method_bind_ptrcall(__{{ method.name }}_method_ptr, nil, raw_data(args), &ret)
    return
}

{% else %}
{{ this.snake_name }}_{{ method.name }} :: proc "contextless" (
{% for arg in method.args %}
    {{ arg.name }}_: {{ arg.type }},
{% end %}
) {
{% for arg in method.args %}
    {{ arg.name }}_ := {{ arg.name }}_
{% end %}
    args := []__bindgen_gde.TypePtr {
{% for arg in method.args %}
        &{{ arg.name }}_,
{% end %}
    }
    __bindgen_gde.object_method_bind_ptrcall(__{{ method.name }}_method_ptr, nil, raw_data(args), nil)
}

{% end %}
{% end %}

{% for method in this.instance_methods %}
{% if return_type, has_return_type := method.return_type.(string); has_return_type %}
{{ this.snake_name }}_{{ method.name }} :: proc "contextless" (
    self: {{ this.self }},
{% for arg in method.args %}
    {{ arg.name }}_: {{ arg.type }},
{% end %}
) -> (ret: {{ return_type }}) {
    self := self
{% for arg in method.args %}
    {{ arg.name }}_ := {{ arg.name }}_
{% end %}
    args := []__bindgen_gde.TypePtr {
{% for arg in method.args %}
        &{{ arg.name }}_,
{% end %}
    }
    __bindgen_gde.object_method_bind_ptrcall(__{{ method.name }}_method_ptr, &self, raw_data(args), &ret)
    return
}

{% else %}
{{ this.snake_name }}_{{ method.name }} :: proc "contextless" (
    self: {{ this.self }},
{% for arg in method.args %}
    {{ arg.name }}_: {{ arg.type }},
{% end %}
) {
    self := self
{% for arg in method.args %}
    {{ arg.name }}_ := {{ arg.name }}_
{% end %}
    args := []__bindgen_gde.TypePtr {
{% for arg in method.args %}
        &{{ arg.name }}_,
{% end %}
    }
    __bindgen_gde.object_method_bind_ptrcall(__{{ method.name }}_method_ptr, &self, raw_data(args), nil)
}

{% end %}
{% end %}

{{ this.snake_name}}_init :: proc "contextless" () {
    __class_name = new_string_name_cstring("{{ this.godot_name }}", true)
    {% if len(this.instance_methods) + len(this.static_methods) > 0 %}
    __name: String_Name
    {% end %}

    {% for method in this.instance_methods %}
    __name = new_string_name_cstring("{{ method.name }}", true)
    __{{ method.name }}_method_ptr = __bindgen_gde.classdb_get_method_bind(&__class_name, &__name, {{ i64(method.hash) }})
    {% end %}
    {% for method in this.static_methods %}
    __name = new_string_name_cstring("{{ method.name }}", true)
    __{{ method.name }}_method_ptr = __bindgen_gde.classdb_get_method_bind(&__class_name, &__name, {{ i64(method.hash) }})
    {% end %}
}

@(private = "file")
__class_name: String_Name

{% for method in this.instance_methods %}
@(private = "file")
__{{ method.name }}_method_ptr: __bindgen_gde.MethodBindPtr
{% end %}
{% for method in this.static_methods %}
@(private = "file")
__{{ method.name }}_method_ptr: __bindgen_gde.MethodBindPtr
{% end %}