/* This file is generated by godin bindgen - DO NOT EDIT! */
package variant

import __bindgen_gde "../gdextension"
{% if this.depends_on_core_math %}
import __bindgen_math "core:math"
{% end %}

{% if builtin_type, is_builtin := this.derived.(StateClass).builtin_info.(StateClassBuiltin); is_builtin %}
when __bindgen_gde.BUILD_CONFIG == "float_32" {
{{ this.odin_type }} :: distinct [{{int(builtin_type.float_32_size)}}]u8
} else when __bindgen_gde.BUILD_CONFIG == "float_64" {
{{ this.odin_type }} :: distinct [{{int(builtin_type.float_64_size)}}]u8
} else when __bindgen_gde.BUILD_CONFIG == "double_32" {
{{ this.odin_type }} :: distinct [{{int(builtin_type.double_32_size)}}]u8
} else when __bindgen_gde.BUILD_CONFIG == "double_64" {
{{ this.odin_type }} :: distinct [{{int(builtin_type.double_64_size)}}]u8
}

// enums
{% for state_enum in this.derived.(StateClass).enums %}

{% if !state_enum.odin_skip %}
{% embed "bindgen_enum.temple.twig" with state_enum.derived.(StateEnum) %}
{% end %}

{% end %}

// constants
{% for constant in this.derived.(StateClass).constants %}
{% if !constant.odin_skip %}
{{ constant.name }}: {{ constant.type.odin_type }} = {% if int_value, is_int := constant.value.(int); is_int %}{{ int(int_value) }}{% else %}{{ constant.value.(string) }}{% end %}
{% end %}
{% end %}

{% if builtin_info, is_builtin := this.derived.(StateClass).builtin_info.(StateClassBuiltin); is_builtin && len(builtin_info.constructors) > 0 %}
// constructors
{{ builtin_info.base_constructor_name }} :: proc {
{% for constructor in builtin_info.constructors %}
    {{ constructor.odin_name }},
{% end %}
{% if this.odin_type == "StringName" %}
    new_string_name_odin,
    new_string_name_cstring,
{% elseif this.odin_type == "String" %}
    new_string_odin,
    new_string_cstring,
{% end %} 
}

{% for constructor, con_index in builtin_info.constructors %}
{{ constructor.odin_name }} :: proc "contextless" ({% for arg, i in constructor.arguments %}{% if i > 0 %}, {% end %}{{ arg.name }}: {{arg.type.odin_type }}{% end %}) -> (ret: {{ this.odin_type }}) {
{% for arg in constructor.arguments %}
    {{ arg.name }} := {{ arg.name }}
{% end %}
    ret = {{ this.odin_type }}{}
    __bindgen_gde.call_builtin_constructor(
        __{{ this.odin_type }}__constructor_{{ int(con_index) }}_backing_ptr,
        &ret
        {% for arg in constructor.arguments %}, &{{ arg.name }}{% end %})
    return
}
{% end %}

{% if destructor_name, has_destructor := builtin_info.destructor_name.(string); has_destructor %}
{{ destructor_name }} :: proc "contextless" (self: {{ this.odin_type }}) {
    self := self
    __{{ this.odin_type }}__destructor_backing_ptr(&self)
}
{% end %}
{% end %}

// member/property frontends
{% for member in this.derived.(StateClass).builtin_info.(StateClassBuiltin).members %}
{{ member.odin_prefix }}_set_{{ member.name }} :: proc "contextless" (self: {{ this.odin_type }}, value: {{ member.type.odin_type }}) {
    self := self
    value := value
    __{{ this.odin_type }}__member__{{ member.name }}_setter(&self, &value)
}

{{ member.odin_prefix }}_get_{{ member.name }} :: proc "contextless" (self: {{ this.odin_type }}) -> (ret: {{ member.type.odin_type }}) {
    self := self
    __{{ this.odin_type }}__member__{{ member.name }}_getter(&self, &ret)
    return
}

{% end %}

// method frontends
{% for method in this.derived.(StateClass).methods %}
{% if !method.odin_skip %}
{{ method.odin_name }} :: proc "contextless" (self: {{ this.odin_type }}
{% for argument in method.arguments %}, {{ argument.name }}: {{ argument.type.odin_type }}
{% end %})
{% if return_type, has_return_type := method.return_type.(^StateType); has_return_type %} -> {{ return_type.odin_type }}{% end %} {
    self := self
{% for argument in method.arguments %}
    {{ argument.name }} := {{ argument.name }}
{% end %}
{% if return_type, has_return_type := method.return_type.(^StateType); has_return_type %}
    return __bindgen_gde.call_builtin_method_ptr_ret(
        __{{ method.odin_name }}__backing_ptr,
        &self,
        {{ return_type.odin_type }},
{% else %}
    __bindgen_gde.call_builtin_method_ptr_no_ret(
        __{{ method.odin_name }}__backing_ptr,
        &self,
{% end %}
{% for argument in method.arguments %}
        &{{ argument.name }},
{% end %}
    )
}

{% end %}
{% end %}

// operator frontends
{% for operator in this.derived.(StateClass).operators %}
{% for overload in operator.overloads %}
{{ overload.odin_name }} :: proc "contextless" (self: {{ this.odin_type }}{% if overload.right_type.odin_type != "Nil" %}, other: {{ overload.right_type.odin_type }}{% end %}) -> (ret: {{ overload.return_type.odin_type }}) {
    self := self
{% if overload.right_type.odin_type != "Nil" %}
    other := other
    return __bindgen_gde.call_builtin_operator_ptr(__{{ overload.odin_name }}__backing_ptr, &self, &other, {{ overload.return_type.odin_type }})
{% else %}
    return __bindgen_gde.call_builtin_operator_ptr(__{{ overload.odin_name }}__backing_ptr, &self, nil, {{ overload.return_type.odin_type }})
{% end %}
}

{% end %}
{{ operator.odin_name }} :: proc {
{% for overload in operator.overloads %}
    {{ overload.odin_name }},
{% end %}
}

{% end %}

// init function
__{{ this.odin_type }}_init :: proc "contextless" () {
    _gde_name := StringName{}

{% if builtin_info, is_builtin := this.derived.(StateClass).builtin_info.(StateClassBuiltin); is_builtin %}
{% for constructor, con_index in builtin_info.constructors %}
    __{{ this.odin_type }}__constructor_{{ int(con_index) }}_backing_ptr = __bindgen_gde.variant_get_ptr_constructor(__bindgen_gde.VariantType.{{ this.variant_type.(string) or_else this.odin_type }}, {{ int(con_index) }})
{% end %}
{% if builtin_info.destructor_name != nil %}
    __{{ this.odin_type }}__destructor_backing_ptr = __bindgen_gde.variant_get_ptr_destructor(__bindgen_gde.VariantType.{{ this.variant_type.(string) or_else this.odin_type }})
{% end %}
{% end %}

{% for member in this.derived.(StateClass).builtin_info.(StateClassBuiltin).members %}
    __bindgen_gde.string_name_new_with_latin1_chars(&_gde_name, "{{ member.name }}", true)
    __{{ this.odin_type }}__member__{{ member.name }}_setter = __bindgen_gde.variant_get_ptr_setter(__bindgen_gde.VariantType.{{ this.variant_type.(string) or_else this.odin_type }}, &_gde_name)
    __{{ this.odin_type }}__member__{{ member.name }}_getter = __bindgen_gde.variant_get_ptr_getter(__bindgen_gde.VariantType.{{ this.variant_type.(string) or_else this.odin_type }}, &_gde_name)
    _gde_name = StringName{}
{% end %}

{% for operator in this.derived.(StateClass).operators %}
{% for overload in operator.overloads %}
    __{{ overload.odin_name }}__backing_ptr = __bindgen_gde.variant_get_ptr_operator_evaluator(__bindgen_gde.VariantOperator.{{ operator.variant_name }}, __bindgen_gde.VariantType.{{ this.odin_type }}, __bindgen_gde.VariantType.{{ overload.right_type.variant_type.(string) or_else overload.right_type.odin_type }})
{% end %}
{% end %}

{% for method in this.derived.(StateClass).methods %}
{% if !method.odin_skip %}
    __bindgen_gde.string_name_new_with_latin1_chars(&_gde_name, "{{ method.godot_name }}", true)
    __{{ method.odin_name }}__backing_ptr = __bindgen_gde.variant_get_ptr_builtin_method(__bindgen_gde.VariantType.{{ this.odin_type }}, &_gde_name, {{ i64(method.hash) }})
    _gde_name = StringName{}
{% end %}
{% end %}
}

{% if builtin_info, is_builtin := this.derived.(StateClass).builtin_info.(StateClassBuiltin); is_builtin %}
// constructor backing procs
{% for constructor, con_index in builtin_info.constructors %}
__{{ this.odin_type }}__constructor_{{ int(con_index) }}_backing_ptr: __bindgen_gde.PtrConstructor
{% end %}
{% if builtin_info.destructor_name != nil %}
__{{ this.odin_type }}__destructor_backing_ptr: __bindgen_gde.PtrDestructor
{% end %}
{% end %}

// method backing ptrs
{% for method in this.derived.(StateClass).methods %}
{% if !method.odin_skip %}
@(private="file")
__{{ method.odin_name }}__backing_ptr: __bindgen_gde.PtrBuiltInMethod
{% end %}
{% end %}
{% end %}

// operator backing ptrs
{% for operator in this.derived.(StateClass).operators %}
{% for overload in operator.overloads %}
@(private="file")
__{{ overload.odin_name }}__backing_ptr: __bindgen_gde.PtrOperatorEvaluator
{% end %}
{% end %}

// getters/setters
{% for member in this.derived.(StateClass).builtin_info.(StateClassBuiltin).members %}
@(private="file")
__{{ this.odin_type }}__member__{{ member.name }}_setter: __bindgen_gde.PtrSetter
@(private="file")
__{{ this.odin_type }}__member__{{ member.name }}_getter: __bindgen_gde.PtrGetter
{% end %}