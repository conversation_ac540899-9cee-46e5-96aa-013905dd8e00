name: 🔗 GHA
on: [push, pull_request, merge_group]

concurrency:
  group: ci-${{ github.actor }}-${{ github.head_ref || github.run_number }}-${{ github.ref }}-runner
  cancel-in-progress: true

jobs:
  # First stage: Only static checks, fast and prevent expensive builds from running.

  static-checks:
    if: '!vars.DISABLE_GODOT_CI'
    name: 📊 Static Checks
    uses: ./.github/workflows/static_checks.yml

  # Second stage: Run all the builds and some of the tests.

  ci:
    name: 🛠️ Continuous Integration
    needs: static-checks
    uses: ./.github/workflows/ci.yml
