# bindgen
TODO generate class signal bindings; N.B. so the user can e.g. connect or emit a signal
TOD<PERSON> create a basic demo game using just the gdextension/, core/, and variant/ packages
TODO generate common global/static StringNames like _process and _ready

TODO refactor multi-passes to do less work
TODO generate Variant constructors that convert from another type (see get_variant_from_type_constructor)
TODO generate Object in core instead of in variant/

TODO rewrite state code to pre-calculate all fields used by templates
TODO add support for vararg utility functions
TODO add option to generate classes and methods in a class-per-package hierarchy (each class gets its own package)

DONE add init proc in editor/
DONE add generation for editor singletons
DONE add init proc in core/
DONE add generation for core singletons
DONE generate special constructors for String, StringName, and Variant (see gdextension/interface.odin)
DONE generate class methods
DONE add generation for native structures

DONE rewrite codegen to use temple templating engine
DONE add support for utility functions with default arg values
DONE add frontend generation for class methods
DONE add generation for engine (non-builtin) classes

# godin
TODO rewrite godin with better Odin parsing support via core:odin/parser
TODO rewrite templating & codegen using the temple template engine


- write libgodin
```c
class_info := Class_Info{
    
}

classdb_class := register_class(class_info)

register_class_method(classdb_class, "method_name", ...details...)

register_class_int_const(classdb_class, "CONST_NAME", CONST_VALUE, is_bitfield=true /*or false*/)

register_class_enum_const(classdb_class, "ENUM_CONST_NAME", My_Enum.Value, is_bitfield=true /*or false*/)

register_class_property(classdb_class, "property_name", VariantType.SomeType, property_getter_proc, property_setter_proc, hint = PropertyHint.None, hint_usage = "...", usage = PropertyUsageFlags.None)

// todo: register_class_property_group (how does it work?!)
// todo: register_class_property_subgroup (how does it work?!)

register_class_signal(classdb_class, "signal_name", args)

unregister_class(classdb_class)
```

- rewrite godin to use core:odin/parser